/**
 * Chat query caching utilities
 * Provides in-memory caching for chat-related queries to reduce database load
 */

export interface ChatCacheEntry {
	data: any;
	timestamp: number;
	userId: string;
}

export class ChatCache {
	private cache = new Map<string, ChatCacheEntry>();
	private ttl: number;
	private cleanupInterval: NodeJS.Timeout | null = null;

	constructor(ttlMinutes: number = 2) {
		this.ttl = ttlMinutes * 60 * 1000; // Convert to milliseconds
		this.startCleanup();
	}

	/**
	 * Generate cache key for user chats
	 */
	private getChatListKey(userId: string, limit: number = 20, offset: number = 0): string {
		return `chats:${userId}:${limit}:${offset}`;
	}

	/**
	 * Get cached chat list if it exists and is not expired
	 */
	getChatList(userId: string, limit: number = 20, offset: number = 0): any | null {
		const key = this.getChatListKey(userId, limit, offset);
		const entry = this.cache.get(key);
		
		if (!entry) {
			return null;
		}

		// Check if expired
		if (Date.now() - entry.timestamp > this.ttl) {
			this.cache.delete(key);
			return null;
		}

		return entry.data;
	}

	/**
	 * Set cached chat list
	 */
	setChatList(userId: string, data: any, limit: number = 20, offset: number = 0): void {
		const key = this.getChatListKey(userId, limit, offset);
		this.cache.set(key, {
			data,
			timestamp: Date.now(),
			userId,
		});
	}

	/**
	 * Invalidate all chat caches for a specific user
	 */
	invalidateUserChats(userId: string): number {
		let invalidated = 0;
		for (const [key, entry] of this.cache.entries()) {
			if (entry.userId === userId || key.includes(`chats:${userId}:`)) {
				this.cache.delete(key);
				invalidated++;
			}
		}
		return invalidated;
	}

	/**
	 * Invalidate all chat caches that might be affected by a new message
	 * This includes caches for all participants in the chat
	 */
	invalidateByMessage(chatId: string, participantUserIds: string[]): number {
		let invalidated = 0;
		
		// Invalidate caches for all participants
		for (const userId of participantUserIds) {
			invalidated += this.invalidateUserChats(userId);
		}
		
		return invalidated;
	}

	/**
	 * Check if a cache entry exists for user chats
	 */
	hasChatList(userId: string, limit: number = 20, offset: number = 0): boolean {
		return this.getChatList(userId, limit, offset) !== null;
	}

	/**
	 * Clear all cache entries
	 */
	clear(): void {
		this.cache.clear();
	}

	/**
	 * Get cache statistics
	 */
	getStats() {
		const now = Date.now();
		const entries = Array.from(this.cache.entries()).map(
			([key, entry]) => ({
				key,
				userId: entry.userId,
				age: now - entry.timestamp,
				expired: now - entry.timestamp > this.ttl,
				dataSize: JSON.stringify(entry.data).length,
			})
		);

		const totalSize = entries.reduce((sum, entry) => sum + entry.dataSize, 0);
		const expiredCount = entries.filter(entry => entry.expired).length;

		return {
			size: this.cache.size,
			ttl: this.ttl,
			totalDataSize: totalSize,
			expiredCount,
			hitRate: this.getHitRate(),
			entries: entries.slice(0, 10), // Only return first 10 for brevity
		};
	}

	/**
	 * Track cache hits and misses for hit rate calculation
	 */
	private hits = 0;
	private misses = 0;

	recordHit(): void {
		this.hits++;
	}

	recordMiss(): void {
		this.misses++;
	}

	getHitRate(): number {
		const total = this.hits + this.misses;
		return total > 0 ? (this.hits / total) * 100 : 0;
	}

	resetStats(): void {
		this.hits = 0;
		this.misses = 0;
	}

	/**
	 * Manually clean up expired entries
	 */
	cleanup(): number {
		const now = Date.now();
		let cleaned = 0;

		for (const [key, entry] of this.cache.entries()) {
			if (now - entry.timestamp > this.ttl) {
				this.cache.delete(key);
				cleaned++;
			}
		}

		return cleaned;
	}

	/**
	 * Start automatic cleanup interval
	 */
	private startCleanup(): void {
		if (this.cleanupInterval) {
			clearInterval(this.cleanupInterval);
		}

		// Clean up every 30 seconds for chat cache (more frequent than redirect cache)
		this.cleanupInterval = setInterval(() => {
			const cleaned = this.cleanup();
			if (cleaned > 0) {
				console.log(
					`[ChatCache] Cleaned up ${cleaned} expired chat cache entries`
				);
			}
		}, 30 * 1000);
	}

	/**
	 * Stop automatic cleanup (useful for testing or shutdown)
	 */
	stopCleanup(): void {
		if (this.cleanupInterval) {
			clearInterval(this.cleanupInterval);
			this.cleanupInterval = null;
		}
	}
}

// Singleton instance for chat caching
export const chatCache = new ChatCache(2); // 2 minutes TTL for chat lists

// Utility functions for common cache operations
export function getCachedChatList(userId: string, limit?: number, offset?: number): any | null {
	const result = chatCache.getChatList(userId, limit, offset);
	if (result) {
		chatCache.recordHit();
		return result;
	} else {
		chatCache.recordMiss();
		return null;
	}
}

export function setCachedChatList(userId: string, data: any, limit?: number, offset?: number): void {
	chatCache.setChatList(userId, data, limit, offset);
}

export function invalidateUserChatCache(userId: string): number {
	return chatCache.invalidateUserChats(userId);
}

export function invalidateChatCacheByMessage(chatId: string, participantUserIds: string[]): number {
	return chatCache.invalidateByMessage(chatId, participantUserIds);
}

// Export cache stats for monitoring
export function getChatCacheStats() {
	return chatCache.getStats();
}
