import { z } from "zod";
import { createTR<PERSON><PERSON>outer as router, protectedProcedure } from "../trpc";
import {
	chats,
	chatParticipants,
	messages,
	users,
	cats,
	catImages,
} from "@/lib/db/schema";
import { eq, and, desc, asc, count, inArray, lt, not } from "drizzle-orm";
import { TRPCError } from "@trpc/server";
import { logSlowQuery, getPerformanceStats } from "./helpers/cat-helpers";
import {
	getCachedChatList,
	setCachedChatList,
	invalidateChatCacheByMessage,
	getChatCacheStats,
} from "@/lib/utils/chat-cache";

// Schema for creating a chat
const createChatSchema = z.object({
	recipientId: z.string(),
	catId: z.string().optional(),
	initialMessage: z.string().min(1, "Message cannot be empty"),
});

// Schema for sending a message
const sendMessageSchema = z.object({
	chatId: z.string(),
	content: z.string().min(1, "Message cannot be empty"),
});

// Schema for starting a conversation about a cat
const startConversationSchema = z.object({
	catId: z.string(),
	message: z.string().min(20, {
		message: "Your message must be at least 20 characters.",
	}),
});

export const messagesRouter = router({
	// Create a new chat
	createChat: protectedProcedure
		.input(createChatSchema)
		.mutation(async ({ ctx, input }) => {
			// Check if recipient exists
			const recipient = await ctx.db.query.users.findFirst({
				where: eq(users.id, parseInt(input.recipientId)),
			});

			if (!recipient) {
				throw new TRPCError({
					code: "NOT_FOUND",
					message: "Recipient not found",
				});
			}

			// Check if chat already exists between these users
			const existingChats = await ctx.db.query.chats.findMany({
				with: {
					participants: true,
				},
			});

			// Find a chat where both users are participants
			const existingChat = existingChats.find((chat) => {
				const participantIds = chat.participants.map((p) => p.userId);
				return (
					participantIds.includes(parseInt(ctx.user.id)) &&
					participantIds.includes(parseInt(input.recipientId))
				);
			});

			let chatId;

			if (existingChat) {
				// Use existing chat
				chatId = existingChat.id;
			} else {
				// Create a new chat
				const [newChat] = await ctx.db
					.insert(chats)
					.values({
						catId: input.catId ? parseInt(input.catId) : null,
					})
					.returning();

				if (!newChat) {
					throw new TRPCError({
						code: "INTERNAL_SERVER_ERROR",
						message: "Failed to create chat",
					});
				}

				// Add both users to the chat
				await ctx.db.insert(chatParticipants).values([
					{
						chatId: newChat.id,
						userId: parseInt(ctx.user.id),
					},
					{
						chatId: newChat.id,
						userId: parseInt(input.recipientId),
					},
				]);

				chatId = newChat.id;
			}

			// Add the initial message
			await ctx.db.insert(messages).values({
				chatId,
				userId: parseInt(ctx.user.id),
				content: input.initialMessage,
			});

			return {
				chatId: chatId.toString(),
				message: "Chat created successfully",
			};
		}),

	// Start a conversation about adopting a cat
	startConversation: protectedProcedure
		.input(startConversationSchema)
		.mutation(async ({ ctx, input }) => {
			// Get the cat to check if it exists and is available
			const cat = await ctx.db.query.cats.findFirst({
				where: eq(cats.id, parseInt(input.catId)),
			});

			if (!cat) {
				throw new TRPCError({
					code: "NOT_FOUND",
					message: "Cat not found",
				});
			}

			if (cat.adopted) {
				throw new TRPCError({
					code: "BAD_REQUEST",
					message: "This cat has already been adopted",
				});
			}

			// Check if user is trying to adopt their own cat
			if (cat.userId === parseInt(ctx.user.id)) {
				throw new TRPCError({
					code: "BAD_REQUEST",
					message: "You cannot adopt your own cat",
				});
			}

			// Check if user already has a conversation for this cat
			const existingChat = await ctx.db.query.chats.findFirst({
				where: eq(chats.catId, parseInt(input.catId)),
				with: {
					participants: {
						where: eq(
							chatParticipants.userId,
							parseInt(ctx.user.id)
						),
					},
				},
			});

			// If chat exists and user is a participant, return that chat
			if (existingChat && existingChat.participants.length > 0) {
				// Add a new message to the existing chat
				await ctx.db.insert(messages).values({
					chatId: existingChat.id,
					userId: parseInt(ctx.user.id),
					content: input.message,
				});

				return {
					chatId: existingChat.id,
					message: "Message sent successfully",
				};
			}

			// Create a new chat for this conversation
			const [newChat] = await ctx.db
				.insert(chats)
				.values({
					catId: parseInt(input.catId),
				})
				.returning();

			if (!newChat) {
				throw new TRPCError({
					code: "INTERNAL_SERVER_ERROR",
					message: "Failed to create chat",
				});
			}

			// Add both users to the chat
			await ctx.db.insert(chatParticipants).values([
				{
					chatId: newChat.id,
					userId: parseInt(ctx.user.id),
				},
				{
					chatId: newChat.id,
					userId: cat.userId,
				},
			]);

			// Add the initial message
			await ctx.db.insert(messages).values({
				chatId: newChat.id,
				userId: parseInt(ctx.user.id),
				content: input.message,
			});

			return {
				chatId: newChat.id,
				message: "Message sent successfully",
			};
		}),

	// Get all chats for the current user (optimized version)
	getMyChats: protectedProcedure
		.input(
			z
				.object({
					limit: z.number().default(20),
					offset: z.number().default(0),
				})
				.optional()
		)
		.query(async ({ ctx, input }) => {
			const startTime = performance.now();
			const { limit = 20, offset = 0 } = input || {};
			const querySteps: string[] = [];

			// Check cache first
			const cacheStart = performance.now();
			const cachedResult = getCachedChatList(ctx.user.id, limit, offset);
			querySteps.push(
				`Cache lookup: ${(performance.now() - cacheStart).toFixed(2)}ms`
			);

			if (cachedResult) {
				const duration = performance.now() - startTime;
				logSlowQuery("getMyChats", duration, 1000, {
					recordCount: cachedResult.length,
					querySteps,
					userId: ctx.user.id,
					cacheHit: true,
				});
				return cachedResult;
			}

			// Step 1: Get user's chat IDs efficiently
			const step1Start = performance.now();
			const userChatIds = await ctx.db
				.select({ chatId: chatParticipants.chatId })
				.from(chatParticipants)
				.where(eq(chatParticipants.userId, parseInt(ctx.user.id)))
				.limit(limit)
				.offset(offset);
			querySteps.push(
				`Get user chat IDs: ${(performance.now() - step1Start).toFixed(2)}ms`
			);

			if (userChatIds.length === 0) {
				const emptyResult: any[] = [];
				// Cache empty result to avoid repeated queries
				setCachedChatList(ctx.user.id, emptyResult, limit, offset);

				const duration = performance.now() - startTime;
				logSlowQuery("getMyChats", duration, 1000, {
					recordCount: 0,
					querySteps,
					userId: ctx.user.id,
					cacheHit: false,
				});
				return emptyResult;
			}

			const chatIds = userChatIds.map((c) => c.chatId);

			// Step 2: Get chat details with optimized joins
			const step2Start = performance.now();
			const chatsData = await ctx.db.query.chats.findMany({
				where: inArray(chats.id, chatIds),
				with: {
					// Only fetch primary cat image
					cat: {
						columns: {
							id: true,
							slug: true,
							name: true,
						},
						with: {
							images: {
								where: eq(catImages.isPrimary, true),
								limit: 1,
							},
						},
					},
					// Get other participants (excluding current user)
					participants: {
						where: (participants) =>
							and(eq(participants.userId, parseInt(ctx.user.id))),
						with: {
							user: {
								columns: {
									id: true,
									slug: true,
									name: true,
									image: true,
									role: true,
								},
							},
						},
					},
					// Get only the latest message
					messages: {
						orderBy: desc(messages.createdAt),
						limit: 1,
						columns: {
							id: true,
							content: true,
							createdAt: true,
							status: true,
							userId: true,
						},
					},
				},
			});
			querySteps.push(
				`Get chat details: ${(performance.now() - step2Start).toFixed(2)}ms`
			);

			// Step 3: Get other participants for each chat in a separate optimized query
			const step3Start = performance.now();
			const otherParticipants =
				await ctx.db.query.chatParticipants.findMany({
					where: and(
						inArray(chatParticipants.chatId, chatIds),
						not(eq(chatParticipants.userId, parseInt(ctx.user.id)))
					),
					with: {
						user: {
							columns: {
								id: true,
								slug: true,
								name: true,
								image: true,
								role: true,
							},
						},
					},
				});
			querySteps.push(
				`Get other participants: ${(performance.now() - step3Start).toFixed(2)}ms`
			);

			// Create a map for quick lookup of other participants
			const otherParticipantsMap = new Map();
			otherParticipants.forEach((participant) => {
				otherParticipantsMap.set(participant.chatId, participant);
			});

			const duration = performance.now() - startTime;
			logSlowQuery("getMyChats", duration, 1000, {
				recordCount: chatsData.length,
				querySteps,
				userId: ctx.user.id,
				cacheHit: false,
			});

			// Step 4: Format the response efficiently
			const result = chatsData
				.map((chat) => {
					const otherParticipant = otherParticipantsMap.get(chat.id);
					const lastMessage = chat.messages[0];
					const primaryImage = chat.cat?.images?.[0];

					return {
						id: chat.id.toString(),
						cat: chat.cat
							? {
									id: chat.cat.id.toString(),
									slug: chat.cat.slug,
									name: chat.cat.name,
									imageUrl:
										primaryImage?.url ||
										"/cat.jpeg?height=300&width=400",
								}
							: null,
						with: otherParticipant
							? {
									id: otherParticipant.user.id.toString(),
									slug: otherParticipant.user.slug,
									name: otherParticipant.user.name || "User",
									image: otherParticipant.user.image,
									role: otherParticipant.user.role,
								}
							: null,
						lastMessage: lastMessage
							? {
									text: lastMessage.content,
									timestamp:
										lastMessage.createdAt.toISOString(),
									isRead: lastMessage.status === "read",
									isFromMe:
										lastMessage.userId ===
										parseInt(ctx.user.id),
								}
							: null,
						createdAt: chat.createdAt,
					};
				})
				.sort((a, b) => {
					// Sort by last message timestamp (newest first)
					const aTime =
						a.lastMessage?.timestamp || a.createdAt.toISOString();
					const bTime =
						b.lastMessage?.timestamp || b.createdAt.toISOString();
					return (
						new Date(bTime).getTime() - new Date(aTime).getTime()
					);
				});

			// Cache the result for future requests
			setCachedChatList(ctx.user.id, result, limit, offset);

			return result;
		}),

	// Get all chats for the current user's cats (as owner)
	getChatsByOwner: protectedProcedure
		.input(
			z.object({
				page: z.number().default(1),
				limit: z.number().default(10),
			})
		)
		.query(async ({ ctx, input }) => {
			const startTime = performance.now();

			// Get cats owned by the user
			const userCats = await ctx.db.query.cats.findMany({
				where: eq(cats.userId, parseInt(ctx.user.id)),
			});

			if (!userCats.length) {
				return {
					chats: [],
					pagination: {
						total: 0,
						pageCount: 0,
						page: input.page,
						limit: input.limit,
					},
				};
			}

			const catIds = userCats.map((cat) => cat.id);

			// Get chats for these cats
			const chatsData = await ctx.db.query.chats.findMany({
				where: inArray(chats.catId, catIds),
				with: {
					cat: {
						with: {
							images: true,
						},
					},
					participants: {
						with: {
							user: true,
						},
						where: (participants) =>
							and(eq(participants.userId, parseInt(ctx.user.id))),
					},
					messages: {
						orderBy: desc(messages.createdAt),
						limit: 1,
					},
				},
				orderBy: desc(chats.createdAt),
				limit: input.limit,
				offset: (input.page - 1) * input.limit,
			});

			// Get total count for pagination
			const [{ value: totalCount }] = await ctx.db
				.select({ value: count() })
				.from(chats)
				.where(inArray(chats.catId, catIds));

			const duration = performance.now() - startTime;
			logSlowQuery("getChatsByOwner", duration);

			// Format the response
			return {
				chats: chatsData.map((chat) => {
					// Find the other participant (not the current user)
					const otherParticipants = chat.participants.filter(
						(p) => p.userId !== parseInt(ctx.user.id)
					);

					return {
						id: chat.id.toString(),
						catId: chat.catId?.toString(),
						catSlug: chat.cat?.slug,
						catName: chat.cat?.name || "Unknown Cat",
						catImage:
							chat.cat?.images?.find((img) => img.isPrimary)
								?.url ||
							chat.cat?.images?.[0]?.url ||
							"/cat.jpeg?height=300&width=400",
						lastMessage: chat.messages[0]?.content || "",
						lastMessageDate:
							chat.messages[0]?.createdAt || chat.createdAt,
						participants: otherParticipants.map((p) => ({
							id: p.user.id.toString(),
							slug: p.user.slug,
							name: p.user.name,
							image: p.user.image,
						})),
					};
				}),
				pagination: {
					total: totalCount,
					pageCount: Math.ceil(totalCount / input.limit),
					page: input.page,
					limit: input.limit,
				},
			};
		}),

	// Get all chats the current user is participating in (as potential adopter)
	getMyChatsByParticipant: protectedProcedure
		.input(
			z.object({
				page: z.number().default(1),
				limit: z.number().default(10),
			})
		)
		.query(async ({ ctx, input }) => {
			// Get chats where the user is a participant
			const userChats = await ctx.db.query.chatParticipants.findMany({
				where: eq(chatParticipants.userId, parseInt(ctx.user.id)),
				with: {
					chat: {
						with: {
							cat: {
								with: {
									images: true,
									user: true,
								},
							},
							participants: {
								with: {
									user: true,
								},
							},
							messages: {
								orderBy: desc(messages.createdAt),
								limit: 1,
							},
						},
					},
				},
				orderBy: desc(chatParticipants.createdAt),
				limit: input.limit,
				offset: (input.page - 1) * input.limit,
			});

			// Get total count for pagination
			const [{ value: totalCount }] = await ctx.db
				.select({ value: count() })
				.from(chatParticipants)
				.where(eq(chatParticipants.userId, parseInt(ctx.user.id)));

			// Format the response
			return {
				chats: userChats.map((chatParticipant) => {
					const chat = chatParticipant.chat;

					// Find the cat owner (not the current user)
					const catOwner = chat.cat?.user;

					return {
						id: chat.id.toString(),
						catId: chat.catId?.toString(),
						catSlug: chat.cat?.slug,
						catName: chat.cat?.name || "Unknown Cat",
						catImage:
							chat.cat?.images?.find((img) => img.isPrimary)
								?.url ||
							chat.cat?.images?.[0]?.url ||
							"/cat.jpeg?height=300&width=400",
						lastMessage: chat.messages[0]?.content || "",
						lastMessageDate:
							chat.messages[0]?.createdAt || chat.createdAt,
						owner: catOwner
							? {
									id: catOwner.id.toString(),
									slug: catOwner.slug,
									name: catOwner.name,
									image: catOwner.image,
								}
							: null,
					};
				}),
				pagination: {
					total: totalCount,
					pageCount: Math.ceil(totalCount / input.limit),
					page: input.page,
					limit: input.limit,
				},
			};
		}),

	// Get messages for a specific chat
	getChatMessages: protectedProcedure
		.input(
			z.object({
				chatId: z.string(),
				limit: z.number().default(50),
				cursor: z.string().optional(), // For pagination
			})
		)
		.query(async ({ ctx, input }) => {
			const chatId = parseInt(input.chatId);

			// Check if user is a participant in this chat
			const isParticipant = await ctx.db.query.chatParticipants.findFirst(
				{
					where: and(
						eq(chatParticipants.chatId, chatId),
						eq(chatParticipants.userId, parseInt(ctx.user.id))
					),
				}
			);

			if (!isParticipant) {
				throw new TRPCError({
					code: "FORBIDDEN",
					message: "You are not a participant in this chat",
				});
			}

			// Get chat details
			const chat = await ctx.db.query.chats.findFirst({
				where: eq(chats.id, chatId),
				with: {
					cat: {
						with: {
							images: true,
						},
					},
					participants: {
						with: {
							user: true,
						},
					},
				},
			});

			if (!chat) {
				throw new TRPCError({
					code: "NOT_FOUND",
					message: "Chat not found",
				});
			}

			// Find the other participant (not the current user)
			const otherParticipant = chat.participants.find(
				(p) => p.userId !== parseInt(ctx.user.id)
			);

			// Build query for messages
			let query = ctx.db.query.messages.findMany({
				where: eq(messages.chatId, chatId),
				orderBy: desc(messages.createdAt),
				limit: input.limit,
				with: {
					user: true,
				},
			});

			// Apply cursor-based pagination if cursor is provided
			if (input.cursor) {
				const cursorDate = new Date(input.cursor);
				query = ctx.db.query.messages.findMany({
					where: and(
						eq(messages.chatId, chatId),
						lt(messages.createdAt, cursorDate)
					),
					orderBy: desc(messages.createdAt),
					limit: input.limit,
					with: {
						user: true,
					},
				});
			}

			const chatMessages = await query;

			// Mark messages as read if they're not from the current user
			const unreadMessageIds = chatMessages
				.filter(
					(msg) =>
						msg.userId !== parseInt(ctx.user.id) &&
						msg.status !== "read"
				)
				.map((msg) => msg.id);

			if (unreadMessageIds.length > 0) {
				await ctx.db
					.update(messages)
					.set({
						status: "read",
					})
					.where(inArray(messages.id, unreadMessageIds));
			}

			return {
				chat: {
					id: chat.id.toString(),
					cat: chat.cat
						? {
								id: chat.cat.id.toString(),
								slug: chat.cat.slug,
								name: chat.cat.name,
								imageUrl:
									chat.cat.images?.find(
										(img) => img.isPrimary
									)?.url ||
									chat.cat.images?.[0]?.url ||
									"/cat.jpeg?height=300&width=400",
								userId: chat.cat.userId.toString(),
								status: chat.cat.status,
							}
						: null,
					with: otherParticipant
						? {
								id: otherParticipant.user.id.toString(),
								slug: otherParticipant.user.slug,
								name: otherParticipant.user.name || "User",
								image: otherParticipant.user.image,
								role: otherParticipant.user.role,
							}
						: null,
				},
				messages: chatMessages.map((msg) => ({
					id: msg.id.toString(),
					content: msg.content,
					timestamp: msg.createdAt.toISOString(),
					status: msg.status,
					isFromMe: msg.userId === parseInt(ctx.user.id),
					sender: {
						id: msg.user.id.toString(),
						name: msg.user.name || "User",
						image: msg.user.image,
					},
				})),
				nextCursor:
					chatMessages.length === input.limit
						? chatMessages[
								chatMessages.length - 1
							].createdAt.toISOString()
						: undefined,
			};
		}),

	// Send a message in a chat
	sendMessage: protectedProcedure
		.input(sendMessageSchema)
		.mutation(async ({ ctx, input }) => {
			const chatId = parseInt(input.chatId);

			// Check if user is a participant in this chat
			const isParticipant = await ctx.db.query.chatParticipants.findFirst(
				{
					where: and(
						eq(chatParticipants.chatId, chatId),
						eq(chatParticipants.userId, parseInt(ctx.user.id))
					),
				}
			);

			if (!isParticipant) {
				throw new TRPCError({
					code: "FORBIDDEN",
					message: "You are not a participant in this chat",
				});
			}

			// Send the message
			const [newMessage] = await ctx.db
				.insert(messages)
				.values({
					chatId,
					userId: parseInt(ctx.user.id),
					content: input.content,
				})
				.returning();

			if (!newMessage) {
				throw new TRPCError({
					code: "INTERNAL_SERVER_ERROR",
					message: "Failed to send message",
				});
			}

			// Invalidate chat caches for all participants
			const allParticipants =
				await ctx.db.query.chatParticipants.findMany({
					where: eq(chatParticipants.chatId, chatId),
				});

			const participantUserIds = allParticipants.map((p) =>
				p.userId.toString()
			);
			const invalidatedCount = invalidateChatCacheByMessage(
				chatId.toString(),
				participantUserIds
			);

			if (invalidatedCount > 0) {
				console.log(
					`[ChatCache] Invalidated ${invalidatedCount} cache entries for new message in chat ${chatId}`
				);
			}

			return {
				id: newMessage.id.toString(),
				content: newMessage.content,
				timestamp: newMessage.createdAt.toISOString(),
				status: newMessage.status,
			};
		}),

	// Get performance statistics for monitoring (admin only)
	getPerformanceStats: protectedProcedure
		.input(z.object({ queryName: z.string().optional() }).optional())
		.query(async ({ ctx, input }) => {
			// Get user from database to check role
			const currentUser = await ctx.db.query.users.findFirst({
				where: eq(users.id, parseInt(ctx.user.id)),
			});

			// Only allow admin users to access performance stats
			if (!currentUser || currentUser.role !== "admin") {
				throw new TRPCError({
					code: "FORBIDDEN",
					message:
						"Only admin users can access performance statistics",
				});
			}

			const stats = getPerformanceStats(input?.queryName);
			const cacheStats = getChatCacheStats();

			return {
				timestamp: new Date().toISOString(),
				queryStats: stats,
				cacheStats,
			};
		}),
});
