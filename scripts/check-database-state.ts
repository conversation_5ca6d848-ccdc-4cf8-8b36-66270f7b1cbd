#!/usr/bin/env tsx

/**
 * <PERSON><PERSON><PERSON> to check the current database state for testing
 */

import { db } from "@/lib/db";
import { users, chats, chatParticipants, messages, cats } from "@/lib/db/schema";
import { count } from "drizzle-orm";

async function checkDatabaseState() {
	console.log("🔍 Checking database state...\n");

	try {
		// Check users count
		const [usersCount] = await db.select({ value: count() }).from(users);
		console.log(`👥 Users: ${usersCount.value}`);

		// Check cats count
		const [catsCount] = await db.select({ value: count() }).from(cats);
		console.log(`🐱 Cats: ${catsCount.value}`);

		// Check chats count
		const [chatsCount] = await db.select({ value: count() }).from(chats);
		console.log(`💬 Chats: ${chatsCount.value}`);

		// Check chat participants count
		const [participantsCount] = await db.select({ value: count() }).from(chatParticipants);
		console.log(`👤 Chat Participants: ${participantsCount.value}`);

		// Check messages count
		const [messagesCount] = await db.select({ value: count() }).from(messages);
		console.log(`📨 Messages: ${messagesCount.value}`);

		// Get a sample user with chats if any exist
		if (usersCount.value > 0 && chatsCount.value > 0) {
			console.log("\n📊 Sample data:");
			
			const sampleUser = await db.query.users.findFirst({
				columns: { id: true, name: true, email: true },
			});
			
			if (sampleUser) {
				console.log(`Sample user: ${sampleUser.name} (${sampleUser.email})`);
				
				// Check if this user has any chats
				const userChats = await db.query.chatParticipants.findMany({
					where: (participants, { eq }) => eq(participants.userId, sampleUser.id),
					limit: 3,
				});
				
				console.log(`User has ${userChats.length} chat participations`);
			}
		}

		console.log("\n✅ Database state check completed!");

	} catch (error) {
		console.error("❌ Error checking database state:", error);
	}
}

// Run the check
checkDatabaseState().catch(console.error);
