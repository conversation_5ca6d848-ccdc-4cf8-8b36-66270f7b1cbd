#!/usr/bin/env tsx

/**
 * Test script for chat caching functionality
 * Tests cache hits, misses, and invalidation
 */

import { db } from "@/lib/db";
import { users, chats, chatParticipants, messages } from "@/lib/db/schema";
import { eq, and, desc, inArray, not } from "drizzle-orm";
import { 
	getCachedChatList, 
	setCachedChatList, 
	invalidateUserChatCache,
	getChatCacheStats 
} from "@/lib/utils/chat-cache";

async function testChatCache() {
	console.log("🚀 Testing chat cache functionality...\n");

	// Find a user with chats for testing
	const chatParticipant = await db.query.chatParticipants.findFirst({
		with: {
			user: true,
		},
	});

	if (!chatParticipant) {
		console.log("❌ No chat participants found in database");
		return;
	}

	const testUser = chatParticipant.user;
	console.log(`📊 Testing with user: ${testUser.name} (ID: ${testUser.id})`);

	// Test 1: Cache miss (first call)
	console.log("\n🔍 Test 1: Cache miss (first call)");
	const startTime1 = performance.now();
	const result1 = getCachedChatList(testUser.id.toString());
	const duration1 = performance.now() - startTime1;
	
	console.log(`   Result: ${result1 ? 'Cache HIT' : 'Cache MISS'} (${duration1.toFixed(2)}ms)`);
	console.log(`   Expected: Cache MISS`);

	// Simulate setting cache data
	const mockChatData = [
		{
			id: "1",
			cat: { id: "1", name: "Test Cat", slug: "test-cat" },
			with: { id: "2", name: "Test User", slug: "test-user" },
			lastMessage: { text: "Hello!", timestamp: new Date().toISOString() },
		}
	];

	// Test 2: Set cache
	console.log("\n💾 Test 2: Setting cache data");
	const startTime2 = performance.now();
	setCachedChatList(testUser.id.toString(), mockChatData);
	const duration2 = performance.now() - startTime2;
	console.log(`   Cache set completed in ${duration2.toFixed(2)}ms`);

	// Test 3: Cache hit (second call)
	console.log("\n✅ Test 3: Cache hit (second call)");
	const startTime3 = performance.now();
	const result3 = getCachedChatList(testUser.id.toString());
	const duration3 = performance.now() - startTime3;
	
	console.log(`   Result: ${result3 ? 'Cache HIT' : 'Cache MISS'} (${duration3.toFixed(2)}ms)`);
	console.log(`   Expected: Cache HIT`);
	console.log(`   Data retrieved: ${result3 ? result3.length : 0} chats`);

	// Test 4: Cache statistics
	console.log("\n📈 Test 4: Cache statistics");
	const stats = getChatCacheStats();
	console.log(`   Cache size: ${stats.size} entries`);
	console.log(`   Hit rate: ${stats.hitRate.toFixed(1)}%`);
	console.log(`   Total data size: ${stats.totalDataSize} bytes`);
	console.log(`   TTL: ${stats.ttl}ms (${(stats.ttl / 1000 / 60).toFixed(1)} minutes)`);

	// Test 5: Cache invalidation
	console.log("\n🗑️  Test 5: Cache invalidation");
	const startTime5 = performance.now();
	const invalidatedCount = invalidateUserChatCache(testUser.id.toString());
	const duration5 = performance.now() - startTime5;
	
	console.log(`   Invalidated ${invalidatedCount} cache entries in ${duration5.toFixed(2)}ms`);

	// Test 6: Cache miss after invalidation
	console.log("\n🔍 Test 6: Cache miss after invalidation");
	const startTime6 = performance.now();
	const result6 = getCachedChatList(testUser.id.toString());
	const duration6 = performance.now() - startTime6;
	
	console.log(`   Result: ${result6 ? 'Cache HIT' : 'Cache MISS'} (${duration6.toFixed(2)}ms)`);
	console.log(`   Expected: Cache MISS`);

	// Final statistics
	console.log("\n📊 Final cache statistics:");
	const finalStats = getChatCacheStats();
	console.log(`   Cache size: ${finalStats.size} entries`);
	console.log(`   Hit rate: ${finalStats.hitRate.toFixed(1)}%`);
	console.log(`   Total requests: ${finalStats.hitRate > 0 ? Math.round((finalStats.size * 100) / finalStats.hitRate) : 'N/A'}`);

	// Performance comparison
	console.log("\n⚡ Performance comparison:");
	console.log(`   Cache miss time: ${duration1.toFixed(2)}ms`);
	console.log(`   Cache hit time: ${duration3.toFixed(2)}ms`);
	if (duration1 > 0 && duration3 > 0) {
		const speedup = duration1 / duration3;
		console.log(`   Cache speedup: ${speedup.toFixed(1)}x faster`);
	}

	console.log("\n✅ Chat cache test completed!");
}

// Run the test
testChatCache().catch(console.error);
