#!/usr/bin/env tsx

/**
 * Performance test script for getMyChats tRPC procedure
 * This script tests the optimized query performance
 */

import { db } from "@/lib/db";
import {
	users,
	chats,
	chatParticipants,
	messages,
	cats,
	catImages,
} from "@/lib/db/schema";
import { eq, and, desc, inArray, not } from "drizzle-orm";

async function testGetMyChatsPerformance() {
	console.log("🚀 Starting getMyChats performance test...\n");

	// Find a user with chats for testing
	const chatParticipant = await db.query.chatParticipants.findFirst({
		with: {
			user: true,
		},
	});

	if (!chatParticipant) {
		console.log("❌ No chat participants found in database");
		return;
	}

	const testUser = chatParticipant.user;

	console.log(`📊 Testing with user: ${testUser.name} (ID: ${testUser.id})`);

	// Test the optimized query multiple times
	const iterations = 5;
	const results = [];

	for (let i = 1; i <= iterations; i++) {
		console.log(`\n🔄 Test iteration ${i}/${iterations}`);
		const startTime = performance.now();

		try {
			// Simulate the optimized getMyChats query
			const limit = 20;
			const offset = 0;

			// Step 1: Get user's chat IDs efficiently
			const userChatIds = await db
				.select({ chatId: chatParticipants.chatId })
				.from(chatParticipants)
				.where(eq(chatParticipants.userId, testUser.id))
				.limit(limit)
				.offset(offset);

			if (userChatIds.length === 0) {
				console.log("   ℹ️  No chats found for user");
				continue;
			}

			const chatIds = userChatIds.map((c) => c.chatId);
			console.log(`   📝 Found ${chatIds.length} chats`);

			// Step 2: Get chat details with optimized joins
			const chatsData = await db.query.chats.findMany({
				where: inArray(chats.id, chatIds),
				with: {
					// Only fetch primary cat image
					cat: {
						columns: {
							id: true,
							slug: true,
							name: true,
						},
						with: {
							images: {
								where: eq(catImages.isPrimary, true),
								limit: 1,
							},
						},
					},
					// Get only the latest message
					messages: {
						orderBy: desc(messages.createdAt),
						limit: 1,
						columns: {
							id: true,
							content: true,
							createdAt: true,
							status: true,
							userId: true,
						},
					},
				},
			});

			// Step 3: Get other participants for each chat in a separate optimized query
			const otherParticipants = await db.query.chatParticipants.findMany({
				where: and(
					inArray(chatParticipants.chatId, chatIds),
					not(eq(chatParticipants.userId, testUser.id))
				),
				with: {
					user: {
						columns: {
							id: true,
							slug: true,
							name: true,
							image: true,
							role: true,
						},
					},
				},
			});

			const duration = performance.now() - startTime;
			results.push(duration);

			console.log(`   ⚡ Query completed in ${duration.toFixed(2)}ms`);
			console.log(
				`   📊 Processed ${chatsData.length} chats with ${otherParticipants.length} participants`
			);
		} catch (error) {
			console.error(`   ❌ Error in iteration ${i}:`, error);
		}
	}

	// Calculate statistics
	if (results.length > 0) {
		const avgTime = results.reduce((a, b) => a + b, 0) / results.length;
		const minTime = Math.min(...results);
		const maxTime = Math.max(...results);

		console.log("\n📈 Performance Results:");
		console.log("=".repeat(50));
		console.log(`Average time: ${avgTime.toFixed(2)}ms`);
		console.log(`Minimum time: ${minTime.toFixed(2)}ms`);
		console.log(`Maximum time: ${maxTime.toFixed(2)}ms`);
		console.log(`Total iterations: ${results.length}`);

		// Performance assessment
		if (avgTime < 200) {
			console.log(
				"✅ EXCELLENT: Query performance is under 200ms target!"
			);
		} else if (avgTime < 500) {
			console.log("✅ GOOD: Query performance is acceptable");
		} else if (avgTime < 1000) {
			console.log("⚠️  MODERATE: Query performance needs improvement");
		} else {
			console.log("❌ POOR: Query performance is too slow (>1000ms)");
		}

		console.log("\n🎯 Target: < 200ms (down from 1680ms baseline)");
		const improvement = ((1680 - avgTime) / 1680) * 100;
		console.log(`📊 Performance improvement: ${improvement.toFixed(1)}%`);
	}

	console.log("\n✅ Performance test completed!");
}

// Run the test
testGetMyChatsPerformance().catch(console.error);
